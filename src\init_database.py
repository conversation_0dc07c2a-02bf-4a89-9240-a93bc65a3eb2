#!/usr/bin/env python3
"""
Initialize database tables for car scraping project
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.models.database import db_manager

def main():
    print("🚀 Initializing database...")
    try:
        db_manager.create_tables()
        print("✅ Database initialization completed successfully!")
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        sys.exit(1)
    finally:
        db_manager.disconnect()

if __name__ == "__main__":
    main()