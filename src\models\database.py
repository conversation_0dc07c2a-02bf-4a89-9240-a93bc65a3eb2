from src.config.database import db_config

class DatabaseManager:
    def __init__(self):
        self.connection = None
    
    def connect(self):
        if not self.connection:
            self.connection = db_config.get_connection()
        return self.connection
    
    def disconnect(self):
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def execute_query(self, query, params=None, fetch=False):
        conn = self.connect()
        cursor = conn.cursor()
        try:
            cursor.execute(query, params or ())
            if fetch:
                return cursor.fetchall()
            else:
                conn.commit()
                return cursor.lastrowid
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            cursor.close()
    
    def create_tables(self):
        """Create all necessary database tables"""
        tables = [
            """
            CREATE TABLE IF NOT EXISTS brands (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL UNIQUE,
                slug VARCHAR(100) NOT NULL UNIQUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """,
            """
            CREATE TABLE IF NOT EXISTS models (
                id INT AUTO_INCREMENT PRIMARY KEY,
                brand_id INT NOT NULL,
                name VARCHAR(100) NOT NULL,
                slug VARCHAR(100) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (brand_id) REFERENCES brands(id),
                UNIQUE KEY unique_brand_model (brand_id, slug)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """,
            """
            CREATE TABLE IF NOT EXISTS years (
                id INT AUTO_INCREMENT PRIMARY KEY,
                year INT NOT NULL UNIQUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """,
            """
            CREATE TABLE IF NOT EXISTS car_version_year (
                id INT AUTO_INCREMENT PRIMARY KEY,
                brand_id INT NOT NULL,
                model_id INT NOT NULL,
                year INT NOT NULL,
                version_name VARCHAR(200) NOT NULL,
                url VARCHAR(500) NOT NULL UNIQUE,
                average_price DECIMAL(15,2) DEFAULT NULL,
                max_price DECIMAL(15,2) DEFAULT NULL,
                min_price DECIMAL(15,2) DEFAULT NULL,
                total_listings INT DEFAULT 0,
                scraped_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (brand_id) REFERENCES brands(id),
                FOREIGN KEY (model_id) REFERENCES models(id),
                INDEX idx_brand_model_year (brand_id, model_id, year)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """,
            """
            CREATE TABLE IF NOT EXISTS car_items (
                id INT AUTO_INCREMENT PRIMARY KEY,
                car_version_year_id INT NOT NULL,
                car_name VARCHAR(300) NOT NULL,
                url VARCHAR(500) NOT NULL UNIQUE,
                price DECIMAL(15,2) NOT NULL,
                location VARCHAR(100) DEFAULT NULL,
                year INT DEFAULT NULL,
                scraped_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (car_version_year_id) REFERENCES car_version_year(id),
                INDEX idx_version_year (car_version_year_id),
                INDEX idx_price (price)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
        ]
        
        for table_sql in tables:
            self.execute_query(table_sql)
        
        print("✅ Database tables created successfully!")

db_manager = DatabaseManager()