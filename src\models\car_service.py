from typing import List, Dict, Optional
from src.models.database import db_manager
import logging

class CarService:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def insert_car_version_year(self, brand_id: int, model_id: int, year: int, 
                               version_name: str, url: str) -> Optional[int]:
        """Insert or update car version year record"""
        try:
            # Check if exists
            existing = db_manager.execute_query("""
                SELECT id FROM car_version_year 
                WHERE brand_id = %s AND model_id = %s AND year = %s AND version_name = %s
            """, (brand_id, model_id, year, version_name), fetch=True)
            
            if existing:
                # Update URL if needed
                db_manager.execute_query("""
                    UPDATE car_version_year SET url = %s, updated_at = NOW()
                    WHERE id = %s
                """, (url, existing[0][0]))
                return existing[0][0]
            
            # Insert new record
            return db_manager.execute_query("""
                INSERT INTO car_version_year (brand_id, model_id, year, version_name, url)
                VALUES (%s, %s, %s, %s, %s)
            """, (brand_id, model_id, year, version_name, url))
            
        except Exception as e:
            self.logger.error(f"Failed to insert car_version_year: {e}")
            return None
    
    def insert_car_item(self, car_version_year_id: int, car_name: str, 
                       url: str, price: int, location: Optional[str] = None, 
                       year: Optional[int] = None) -> Optional[int]:
        """Insert car item record"""
        try:
            # Check if exists
            existing = db_manager.execute_query("""
                SELECT id FROM car_items WHERE url = %s
            """, (url,), fetch=True)
            
            if existing:
                # Update existing record
                db_manager.execute_query("""
                    UPDATE car_items 
                    SET car_name = %s, price = %s, location = %s, year = %s, 
                        scraped_at = NOW(), updated_at = NOW()
                    WHERE id = %s
                """, (car_name, price, location, year, existing[0][0]))
                return existing[0][0]
            
            # Insert new record
            return db_manager.execute_query("""
                INSERT INTO car_items (car_version_year_id, car_name, url, price, location, year)
                VALUES (%s, %s, %s, %s, %s, %s)
            """, (car_version_year_id, car_name, url, price, location, year))
            
        except Exception as e:
            self.logger.error(f"Failed to insert car_item: {e}")
            return None
    
    def update_car_version_prices(self, car_version_year_id: int):
        """Update price statistics for a car version year"""
        try:
            # Calculate price statistics
            stats = db_manager.execute_query("""
                SELECT 
                    COUNT(*) as total_listings,
                    AVG(price) as avg_price,
                    MIN(price) as min_price,
                    MAX(price) as max_price
                FROM car_items 
                WHERE car_version_year_id = %s
            """, (car_version_year_id,), fetch=True)
            
            if stats and stats[0][0] > 0:  # If there are listings
                total_listings, avg_price, min_price, max_price = stats[0]
                
                db_manager.execute_query("""
                    UPDATE car_version_year 
                    SET total_listings = %s, average_price = %s, min_price = %s, 
                        max_price = %s, scraped_at = NOW(), updated_at = NOW()
                    WHERE id = %s
                """, (total_listings, avg_price, min_price, max_price, car_version_year_id))
                
                self.logger.info(f"Updated prices for car_version_year_id {car_version_year_id}: "
                               f"{total_listings} listings, avg: {avg_price:,.0f} VND")
            
        except Exception as e:
            self.logger.error(f"Failed to update car version prices: {e}")
    
    def get_all_brands(self) -> List[Dict]:
        """Get all brands from database"""
        try:
            brands = db_manager.execute_query("""
                SELECT id, name, slug FROM brands ORDER BY name
            """, fetch=True)
            
            return [{'id': row[0], 'name': row[1], 'slug': row[2]} for row in brands]
        except Exception as e:
            self.logger.error(f"Failed to get brands: {e}")
            return []
    
    def get_brand_models(self, brand_id: int) -> List[Dict]:
        """Get models for a specific brand"""
        try:
            models = db_manager.execute_query("""
                SELECT id, name, slug FROM models 
                WHERE brand_id = %s ORDER BY name
            """, (brand_id,), fetch=True)
            
            return [{'id': row[0], 'name': row[1], 'slug': row[2]} for row in models]
        except Exception as e:
            self.logger.error(f"Failed to get models for brand {brand_id}: {e}")
            return []
    
    def insert_year(self, year: int) -> Optional[int]:
        """Insert year record"""
        try:
            # Check if exists
            existing = db_manager.execute_query("""
                SELECT id FROM years WHERE year = %s
            """, (year,), fetch=True)
            
            if existing:
                return existing[0][0]
            
            # Insert new year
            return db_manager.execute_query("""
                INSERT INTO years (year) VALUES (%s)
            """, (year,))
            
        except Exception as e:
            self.logger.error(f"Failed to insert year {year}: {e}")
            return None
    
    def get_car_versions_to_scrape(self, limit: Optional[int] = None) -> List[Dict]:
        """Get car versions that need to be scraped for listings"""
        try:
            query = """
                SELECT cv.id, cv.url, b.name as brand_name, m.name as model_name, 
                       cv.year, cv.version_name, cv.scraped_at
                FROM car_version_year cv
                JOIN brands b ON cv.brand_id = b.id
                JOIN models m ON cv.model_id = m.id
                WHERE cv.scraped_at IS NULL OR cv.scraped_at < DATE_SUB(NOW(), INTERVAL 1 DAY)
                ORDER BY cv.year DESC, b.name, m.name
            """
            
            if limit:
                query += f" LIMIT {limit}"
            
            versions = db_manager.execute_query(query, fetch=True)
            
            return [{
                'id': row[0],
                'url': row[1], 
                'brand_name': row[2],
                'model_name': row[3],
                'year': row[4],
                'version_name': row[5],
                'scraped_at': row[6]
            } for row in versions]
            
        except Exception as e:
            self.logger.error(f"Failed to get car versions to scrape: {e}")
            return []

    def get_brands_count(self) -> int:
        """Get total number of brands in database"""
        try:
            result = db_manager.execute_query("SELECT COUNT(*) FROM brands", fetch=True)
            return result[0][0] if result else 0
        except Exception as e:
            self.logger.error(f"Failed to get brands count: {e}")
            return 0

    def get_models_count(self) -> int:
        """Get total number of models in database"""
        try:
            result = db_manager.execute_query("SELECT COUNT(*) FROM models", fetch=True)
            return result[0][0] if result else 0
        except Exception as e:
            self.logger.error(f"Failed to get models count: {e}")
            return 0

    def get_all_brands_with_models(self) -> List[Dict]:
        """Get all brands with their models from database"""
        try:
            # Get all brands
            brands_result = db_manager.execute_query("""
                SELECT id, name, slug FROM brands ORDER BY name
            """, fetch=True)

            if not brands_result:
                return []

            brands_data = []
            for brand_row in brands_result:
                brand_id, brand_name, brand_slug = brand_row

                # Get models for this brand
                models_result = db_manager.execute_query("""
                    SELECT id, name, slug FROM models WHERE brand_id = %s ORDER BY name
                """, (brand_id,), fetch=True)

                models = []
                if models_result:
                    models = [{
                        'id': model_row[0],
                        'name': model_row[1],
                        'slug': model_row[2]
                    } for model_row in models_result]

                brands_data.append({
                    'id': brand_id,
                    'name': brand_name,
                    'slug': brand_slug,
                    'models': models
                })

            return brands_data

        except Exception as e:
            self.logger.error(f"Failed to get brands with models: {e}")
            return []

car_service = CarService()