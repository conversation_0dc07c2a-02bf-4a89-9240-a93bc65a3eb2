<PERSON>, h<PERSON><PERSON> thực hiện các bước sau theo thứ tự:

**Bước 1: Thi<PERSON><PERSON> lập dự án**
1. Active dự án hiện tại trong workspace
2. Tạo initial instructions cho dự án
3. Th<PERSON><PERSON> hiện onboarding dự án

**Bước 2: Phân tích cấu trúc website mục tiêu**
Truy cập URL mẫu: https://bonbanh.com/oto/bmw-x3-nam-2025 để phân tích cấu trúc HTML và hiểu các thành phần chính:
- <PERSON>u hiển thị hãng xe và model xe
- Box hiển thị các năm sản xuất (đời xe) đang có bán
- Box hiển thị các phiên bản xe của từng đời

Từ URL ví dụ, xác định được:
- Hãng xe: BMW
- Model xe: X3  
- Đời xe: 2025
- <PERSON><PERSON><PERSON> <PERSON>hiê<PERSON> bản xe: sDrive20i M Sport, xDrive20i, xDrive20i M Sport

**Bước 3: <PERSON><PERSON><PERSON> triển hệ thống crawl dữ liệu**
Sử dụng Python để xây dựng hệ thống crawl dữ liệu với cấu trúc thư mục hợp lý (không để tất cả file ở thư mục gốc). Hệ thống cần:

1. **Crawl và lưu trữ dữ liệu chỉ mục vào MySQL** (thông tin kết nối từ file .env):
   - Bảng brands: Danh sách hãng xe
   - Bảng models: Danh sách model xe theo hãng
   - Bảng years: Danh sách các đời xe
   - Bảng car_version_year với các cột:
     * brand_id (foreign key)
     * model_id (foreign key) 
     * year (năm sản xuất)
     * version_name (tên phiên bản)
     * url (URL đầy đủ, ví dụ: https://bonbanh.com/oto/bmw-x3-sdrive20i-m-sport-nam-2025)
     * average_price (giá trung bình)
     * max_price (giá cao nhất)
     * min_price (giá thấp nhất)

2. **Crawl chi tiết từng xe cụ thể**:
   - Với mỗi URL phiên bản xe (như https://bonbanh.com/oto/bmw-x3-sdrive20i-m-sport-nam-2025)
   - Lấy dữ liệu từ 3 trang đầu tiên của danh sách xe
   - Lưu vào bảng car_items với các cột:
     * car_version_year_id (foreign key)
     * car_name (ví dụ: "Xe BMW X3 sDrive20i M Sport 2025")
     * url (ví dụ: https://bonbanh.com/xe-bmw-x3-sdrive20i-m-sport-2025-5765980)
     * price (giá bán, ví dụ: 2189000000 cho "2.189.000.000 (2 Tỷ 189 Triệu)")

3. **Tính toán và cập nhật giá**:
   - Sau khi crawl xong car_items, tính toán lại average_price, min_price, max_price cho bảng car_version_year dựa trên dữ liệu thực tế

**Yêu cầu kỹ thuật**:
- Sử dụng Python với các thư viện phù hợp cho web scraping
- Kết nối MySQL sử dụng thông tin từ file .env
- Tổ chức code theo cấu trúc thư mục rõ ràng và logic
- Xử lý lỗi và retry khi cần thiết
- Ghi log quá trình crawl để theo dõi