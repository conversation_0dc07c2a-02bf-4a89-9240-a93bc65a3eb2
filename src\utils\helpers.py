import re
import time
import random
from typing import Optional

def parse_vietnamese_price(price_text: str) -> Optional[int]:
    """
    Parse Vietnamese price text to integer value
    Examples: 
    - "************* (2 Tỷ 189 Triệu)" -> 2189000000
    - "850 triệu" -> 850000000
    - "1,5 tỷ" -> 1500000000
    """
    if not price_text:
        return None
        
    # Clean the text
    price_text = price_text.lower().strip()
    
    # Try to extract number from parentheses first (more reliable)
    parentheses_match = re.search(r'\(([^)]+)\)', price_text)
    if parentheses_match:
        price_text = parentheses_match.group(1)
    
    # Remove common words
    price_text = re.sub(r'(vnd|đồng|giá|bán|từ|đến)', '', price_text)
    
    # Parse patterns like "2 tỷ 189 triệu"
    ty_trieu_match = re.search(r'(\d+(?:[.,]\d+)?)\s*tỷ\s*(\d+(?:[.,]\d+)?)\s*triệu', price_text)
    if ty_trieu_match:
        ty = float(ty_trieu_match.group(1).replace(',', '.'))
        trieu = float(ty_trieu_match.group(2).replace(',', '.'))
        return int(ty * 1_000_000_000 + trieu * 1_000_000)
    
    # Parse patterns like "2 tỷ"
    ty_match = re.search(r'(\d+(?:[.,]\d+)?)\s*tỷ', price_text)
    if ty_match:
        ty = float(ty_match.group(1).replace(',', '.'))
        return int(ty * 1_000_000_000)
    
    # Parse patterns like "850 triệu"
    trieu_match = re.search(r'(\d+(?:[.,]\d+)?)\s*triệu', price_text)
    if trieu_match:
        trieu = float(trieu_match.group(1).replace(',', '.'))
        return int(trieu * 1_000_000)
    
    # Try to parse direct numeric format like "*************"
    numeric_match = re.search(r'(\d{1,3}(?:\.\d{3})+)', price_text)
    if numeric_match:
        number_str = numeric_match.group(1).replace('.', '')
        return int(number_str)
    
    return None

def slugify(text: str) -> str:
    """Convert text to URL-friendly slug"""
    if not text:
        return ""
    
    # Convert to lowercase
    text = text.lower()
    
    # Replace Vietnamese characters
    vietnamese_map = {
        'á': 'a', 'à': 'a', 'ả': 'a', 'ã': 'a', 'ạ': 'a',
        'ă': 'a', 'ắ': 'a', 'ằ': 'a', 'ẳ': 'a', 'ẵ': 'a', 'ặ': 'a',
        'â': 'a', 'ấ': 'a', 'ầ': 'a', 'ẩ': 'a', 'ẫ': 'a', 'ậ': 'a',
        'é': 'e', 'è': 'e', 'ẻ': 'e', 'ẽ': 'e', 'ẹ': 'e',
        'ê': 'e', 'ế': 'e', 'ề': 'e', 'ể': 'e', 'ễ': 'e', 'ệ': 'e',
        'í': 'i', 'ì': 'i', 'ỉ': 'i', 'ĩ': 'i', 'ị': 'i',
        'ó': 'o', 'ò': 'o', 'ỏ': 'o', 'õ': 'o', 'ọ': 'o',
        'ô': 'o', 'ố': 'o', 'ồ': 'o', 'ổ': 'o', 'ỗ': 'o', 'ộ': 'o',
        'ơ': 'o', 'ớ': 'o', 'ờ': 'o', 'ở': 'o', 'ỡ': 'o', 'ợ': 'o',
        'ú': 'u', 'ù': 'u', 'ủ': 'u', 'ũ': 'u', 'ụ': 'u',
        'ư': 'u', 'ứ': 'u', 'ừ': 'u', 'ử': 'u', 'ữ': 'u', 'ự': 'u',
        'ý': 'y', 'ỳ': 'y', 'ỷ': 'y', 'ỹ': 'y', 'ỵ': 'y',
        'đ': 'd'
    }
    
    for vn_char, en_char in vietnamese_map.items():
        text = text.replace(vn_char, en_char)
    
    # Replace spaces and special characters with hyphens
    text = re.sub(r'[^a-z0-9]+', '-', text)
    
    # Remove leading/trailing hyphens
    text = text.strip('-')
    
    return text

def random_delay(min_seconds: float = 1.0, max_seconds: float = 3.0):
    """Random delay to avoid being blocked"""
    delay = random.uniform(min_seconds, max_seconds)
    time.sleep(delay)

def extract_year_from_url(url: str) -> Optional[int]:
    """Extract year from URL pattern like /nam-2025"""
    match = re.search(r'nam-(\d{4})', url)
    return int(match.group(1)) if match else None