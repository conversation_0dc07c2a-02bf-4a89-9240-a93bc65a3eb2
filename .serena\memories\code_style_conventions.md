## Code Style & Conventions
- Follow PEP8 for formatting
- Use type hints for function signatures
- Add docstrings for all modules, classes, and functions
- Organize code into logical folders (e.g., `src/`, `db/`, `scraper/`, `models/`)
- Use environment variables from `.env` for configuration
- Log all scraping and database operations
- Handle exceptions and retries gracefully
- Use descriptive variable and function names
- No code in the project root