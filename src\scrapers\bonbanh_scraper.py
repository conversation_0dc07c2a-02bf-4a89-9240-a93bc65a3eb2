import requests
from bs4 import BeautifulSoup
import re
from typing import List, Dict, Optional
import logging
from urllib.parse import urljoin, urlparse
import os
from dotenv import load_dotenv

from src.utils.helpers import parse_vietnamese_price, slugify, random_delay, extract_year_from_url
from src.models.database import db_manager

load_dotenv()

class BonbanhScraper:
    def __init__(self):
        self.base_url = "https://bonbanh.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': os.getenv('USER_AGENT', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'vi-VN,vi;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        self.timeout = int(os.getenv('TIMEOUT', 30))
        self.max_retries = int(os.getenv('MAX_RETRIES', 3))
        
        logging.basicConfig(level=getattr(logging, os.getenv('LOG_LEVEL', 'INFO')))
        self.logger = logging.getLogger(__name__)

    def get_page(self, url: str) -> Optional[BeautifulSoup]:
        """Get and parse a web page"""
        for attempt in range(self.max_retries):
            try:
                random_delay()
                response = self.session.get(url, timeout=self.timeout)
                response.raise_for_status()
                
                return BeautifulSoup(response.content, 'html.parser')
                
            except Exception as e:
                self.logger.warning(f"Attempt {attempt + 1} failed for {url}: {e}")
                if attempt == self.max_retries - 1:
                    self.logger.error(f"Failed to get page after {self.max_retries} attempts: {url}")
                    return None
        
        return None

    def scrape_brands_and_models(self) -> List[Dict]:
        """Scrape car brands and their models from the homepage"""
        self.logger.info("🚀 Starting to scrape brands and models...")
        
        soup = self.get_page(self.base_url)
        if not soup:
            return []
        
        brands_data = []
        
        # Look for brand navigation or menu
        brand_links = soup.find_all('a', href=re.compile(r'/oto/[\w-]+/?$'))
        
        seen_brands = set()
        for link in brand_links:
            href = link.get('href', '')
            brand_name = link.get_text().strip()
            
            if not brand_name or brand_name.lower() in seen_brands:
                continue
            
            seen_brands.add(brand_name.lower())
            brand_slug = slugify(brand_name)
            
            # Insert or get brand
            brand_id = self._insert_brand(brand_name, brand_slug)
            if brand_id:
                # Scrape models for this brand
                models = self._scrape_brand_models(brand_slug, brand_id)
                brands_data.append({
                    'id': brand_id,
                    'name': brand_name,
                    'slug': brand_slug,
                    'models': models
                })
        
        self.logger.info(f"✅ Scraped {len(brands_data)} brands")
        return brands_data

    def _insert_brand(self, name: str, slug: str) -> Optional[int]:
        """Insert brand into database"""
        try:
            # Check if exists
            existing = db_manager.execute_query(
                "SELECT id FROM brands WHERE slug = %s", (slug,), fetch=True
            )
            
            if existing:
                return existing[0][0]
            
            # Insert new brand
            return db_manager.execute_query(
                "INSERT INTO brands (name, slug) VALUES (%s, %s)",
                (name, slug)
            )
        except Exception as e:
            self.logger.error(f"Failed to insert brand {name}: {e}")
            return None

    def _insert_model(self, brand_id: int, name: str, slug: str) -> Optional[int]:
        """Insert model into database"""
        try:
            # Check if exists
            existing = db_manager.execute_query(
                "SELECT id FROM models WHERE brand_id = %s AND slug = %s", 
                (brand_id, slug), fetch=True
            )
            
            if existing:
                return existing[0][0]
            
            # Insert new model
            return db_manager.execute_query(
                "INSERT INTO models (brand_id, name, slug) VALUES (%s, %s, %s)",
                (brand_id, name, slug)
            )
        except Exception as e:
            self.logger.error(f"Failed to insert model {name}: {e}")
            return None

    def _scrape_brand_models(self, brand_slug: str, brand_id: int) -> List[Dict]:
        """Scrape models for a specific brand"""
        models = []
        brand_url = f"{self.base_url}/oto/{brand_slug}"
        
        soup = self.get_page(brand_url)
        if not soup:
            return models
        
        # Look for model links
        model_links = soup.find_all('a', href=re.compile(rf'/oto/{brand_slug}-[\w-]+/?'))
        
        seen_models = set()
        for link in model_links:
            href = link.get('href', '')
            model_text = link.get_text().strip()
            
            if not model_text or model_text.lower() in seen_models:
                continue
            
            # Extract model name from URL or text
            model_match = re.search(rf'/oto/{brand_slug}-([\w-]+)', href)
            if model_match:
                model_slug = model_match.group(1)
                model_name = model_text
                
                seen_models.add(model_name.lower())
                
                model_id = self._insert_model(brand_id, model_name, model_slug)
                if model_id:
                    models.append({
                        'id': model_id,
                        'name': model_name,
                        'slug': model_slug
                    })
        
        return models

    def scrape_car_versions(self, brand_slug: str, model_slug: str, year: int) -> List[Dict]:
        """Scrape car versions for a specific brand/model/year"""
        versions = []
        url = f"{self.base_url}/oto/{brand_slug}-{model_slug}-nam-{year}"
        
        soup = self.get_page(url)
        if not soup:
            return versions
        
        # Look for version links
        version_links = soup.find_all('a', href=re.compile(rf'/oto/{brand_slug}-{model_slug}-[\w-]+-nam-{year}'))
        
        seen_versions = set()
        for link in version_links:
            href = link.get('href', '')
            version_text = link.get_text().strip()
            
            if href in seen_versions:
                continue
            
            seen_versions.add(href)
            
            # Extract version name from URL
            version_match = re.search(rf'/oto/{brand_slug}-{model_slug}-([\w-]+)-nam-{year}', href)
            if version_match:
                version_slug = version_match.group(1)
                version_name = version_text or version_slug.replace('-', ' ').title()
                
                full_url = urljoin(self.base_url, href)
                
                versions.append({
                    'version_name': version_name,
                    'url': full_url,
                    'slug': version_slug
                })
        
        return versions

    def scrape_car_listings(self, version_url: str, max_pages: int = 3) -> List[Dict]:
        """Scrape individual car listings from version page"""
        listings = []
        
        for page in range(1, max_pages + 1):
            page_url = f"{version_url}?page={page}" if page > 1 else version_url
            soup = self.get_page(page_url)
            
            if not soup:
                continue
            
            # Look for car listing elements
            car_items = soup.find_all(['div', 'article'], class_=re.compile(r'car|item|listing|product'))
            
            for item in car_items:
                listing_data = self._extract_listing_data(item)
                if listing_data:
                    listings.append(listing_data)
            
            # Check if there's a next page
            if not self._has_next_page(soup):
                break
        
        return listings

    def _extract_listing_data(self, item_element) -> Optional[Dict]:
        """Extract data from a single car listing element"""
        try:
            # Find link to individual car
            link = item_element.find('a', href=re.compile(r'/xe-[\w-]+-\d+'))
            if not link:
                return None
            
            car_url = urljoin(self.base_url, link.get('href'))
            car_name = link.get_text().strip()
            
            # Find price
            price_element = item_element.find(['span', 'div'], string=re.compile(r'(tỷ|triệu|\.000\.000)'))
            price_text = price_element.get_text() if price_element else ""
            price = parse_vietnamese_price(price_text)
            
            if not price:
                return None
            
            # Find location if available
            location_element = item_element.find(['span', 'div'], string=re.compile(r'(Hà Nội|TP HCM|Đà Nẵng|Cần Thơ)'))
            location = location_element.get_text().strip() if location_element else None
            
            # Extract year from car name or URL
            year_match = re.search(r'(19|20)\d{2}', car_name)
            year = int(year_match.group()) if year_match else None
            
            return {
                'car_name': car_name,
                'url': car_url,
                'price': price,
                'location': location,
                'year': year
            }
            
        except Exception as e:
            self.logger.debug(f"Failed to extract listing data: {e}")
            return None

    def _has_next_page(self, soup: BeautifulSoup) -> bool:
        """Check if there's a next page available"""
        next_link = soup.find('a', string=re.compile(r'(Tiếp|Next|>>)'))
        return next_link is not None