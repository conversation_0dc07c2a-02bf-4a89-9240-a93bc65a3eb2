#!/usr/bin/env python3
"""
Main orchestrator for car scraping system
"""

import sys
import os
import logging
import argparse
from datetime import datetime
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.scrapers.bonbanh_scraper import Bonban<PERSON><PERSON>craper
from src.models.car_service import car_service
from src.models.database import db_manager

class CarScrapingOrchestrator:
    def __init__(self):
        self.scraper = BonbanhScraper()
        self.logger = logging.getLogger(__name__)
        
        # Create logs directory if it doesn't exist
        os.makedirs('logs', exist_ok=True)
        
        # Set up file logging with UTF-8 encoding
        file_handler = logging.FileHandler('logs/scraper.log', encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)

        # Set up console logging with UTF-8 encoding
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)

        # Set logger level
        self.logger.setLevel(logging.INFO)

    def _check_brands_models_exist(self) -> bool:
        """Check if brands and models already exist in database"""
        try:
            brands_count = car_service.get_brands_count()
            models_count = car_service.get_models_count()

            if brands_count > 0 and models_count > 0:
                self.logger.info(f"[INFO] Found {brands_count} brands and {models_count} models in database")
                return True
            else:
                self.logger.info("[INFO] No brands/models found in database")
                return False
        except Exception as e:
            self.logger.error(f"[ERROR] Failed to check brands/models: {e}")
            return False

    def scrape_index_data(self, skip_brands_models: bool = False, force_refresh: bool = False):
        """Scrape and store index data (brands, models, car versions)"""
        self.logger.info("[START] Starting index data scraping...")

        # Step 1: Check if we need to scrape brands and models
        should_scrape_brands_models = True

        if skip_brands_models:
            self.logger.info("[SKIP] Skipping brands and models scraping as requested")
            should_scrape_brands_models = False
        elif not force_refresh and self._check_brands_models_exist():
            self.logger.info("[SKIP] Brands and models already exist. Use --force-refresh to update them")
            should_scrape_brands_models = False

        # Step 2: Scrape brands and models if needed
        if should_scrape_brands_models:
            self.logger.info("[SCRAPE] Scraping brands and models...")
            brands_data = self.scraper.scrape_brands_and_models()

            if not brands_data:
                self.logger.error("[ERROR] No brands data found. Check the scraper logic.")
                return False
        else:
            # Get existing brands data from database
            brands_data = car_service.get_all_brands_with_models()

            if not brands_data:
                self.logger.error("[ERROR] No brands data found in database. Please run without --skip-brands-models first.")
                return False

        # Step 3: For each brand/model combination, scrape available years and versions
        years_to_scrape = [2020, 2021, 2022, 2023, 2024, 2025]  # Focus on recent years
        
        for brand in brands_data:
            brand_id = brand['id']
            brand_slug = brand['slug']
            
            self.logger.info(f"[BRAND] Processing brand: {brand['name']}")
            
            for model in brand['models']:
                model_id = model['id']
                model_slug = model['slug']
                
                self.logger.info(f"  [MODEL] Processing model: {model['name']}")
                
                for year in years_to_scrape:
                    # Insert year if not exists
                    car_service.insert_year(year)
                    
                    # Scrape versions for this brand/model/year
                    versions = self.scraper.scrape_car_versions(brand_slug, model_slug, year)
                    
                    for version in versions:
                        version_id = car_service.insert_car_version_year(
                            brand_id=brand_id,
                            model_id=model_id,
                            year=year,
                            version_name=version['version_name'],
                            url=version['url']
                        )
                        
                        if version_id:
                            self.logger.info(f"    [SUCCESS] Added version: {version['version_name']} ({year})")
        
        self.logger.info("[COMPLETE] Index data scraping completed!")
        return True

    def scrape_car_listings(self, limit_versions: int = 50):
        """Scrape individual car listings for stored versions"""
        self.logger.info("[START] Starting car listings scraping...")
        
        # Get versions that need to be scraped
        versions_to_scrape = car_service.get_car_versions_to_scrape(limit=limit_versions)
        
        if not versions_to_scrape:
            self.logger.info("[INFO] No versions need to be scraped at this time.")
            return True
        
        self.logger.info(f"[INFO] Found {len(versions_to_scrape)} versions to scrape")
        
        for i, version in enumerate(versions_to_scrape, 1):
            self.logger.info(f"[{i}/{len(versions_to_scrape)}] Scraping: {version['brand_name']} "
                           f"{version['model_name']} {version['version_name']} ({version['year']})")
            
            # Scrape listings for this version (first 3 pages)
            listings = self.scraper.scrape_car_listings(version['url'], max_pages=3)
            
            if not listings:
                self.logger.warning(f"[WARNING] No listings found for {version['url']}")
                continue
            
            # Insert listings into database
            inserted_count = 0
            for listing in listings:
                item_id = car_service.insert_car_item(
                    car_version_year_id=version['id'],
                    car_name=listing['car_name'],
                    url=listing['url'],
                    price=listing['price'],
                    location=listing.get('location'),
                    year=listing.get('year')
                )
                
                if item_id:
                    inserted_count += 1
            
            self.logger.info(f"  [SUCCESS] Inserted {inserted_count} listings")
            
            # Update price statistics
            car_service.update_car_version_prices(version['id'])
            
        self.logger.info("[COMPLETE] Car listings scraping completed!")
        return True

    def run_full_scrape(self, skip_brands_models: bool = False, force_refresh: bool = False):
        """Run complete scraping process"""
        start_time = datetime.now()
        self.logger.info(f"[START] Starting full scraping process at {start_time}")
        
        try:
            # Step 1: Scrape index data
            if not self.scrape_index_data(skip_brands_models=skip_brands_models, force_refresh=force_refresh):
                self.logger.error("[ERROR] Index data scraping failed")
                return False
            
            # Step 2: Scrape car listings
            if not self.scrape_car_listings():
                self.logger.error("[ERROR] Car listings scraping failed")
                return False
            
            end_time = datetime.now()
            duration = end_time - start_time
            self.logger.info(f"[COMPLETE] Full scraping process completed in {duration}")
            
            return True
            
        except KeyboardInterrupt:
            self.logger.info("[STOP] Scraping interrupted by user")
            return False
        except Exception as e:
            self.logger.error(f"[ERROR] Scraping failed with error: {e}", exc_info=True)
            return False
        finally:
            db_manager.disconnect()

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Car scraping system')
    parser.add_argument('command', choices=['index', 'listings', 'full'],
                       help='Command to run')
    parser.add_argument('limit', type=int, nargs='?', default=50,
                       help='Limit for listings command (default: 50)')
    parser.add_argument('--skip-brands-models', action='store_true',
                       help='Skip scraping brands and models, use existing data')
    parser.add_argument('--only-versions', action='store_true',
                       help='Alias for --skip-brands-models')
    parser.add_argument('--force-refresh', action='store_true',
                       help='Force refresh all data including brands and models')

    # Handle old-style command line arguments for backward compatibility
    if len(sys.argv) > 1 and sys.argv[1] not in ['index', 'listings', 'full']:
        print("Usage: python run.py [index|listings|full] [options]")
        print("Options:")
        print("  --skip-brands-models    Skip scraping brands and models")
        print("  --only-versions         Alias for --skip-brands-models")
        print("  --force-refresh         Force refresh all data")
        print("\nExamples:")
        print("  python run.py index                    # Full index scraping")
        print("  python run.py index --skip-brands-models  # Only scrape versions")
        print("  python run.py index --force-refresh    # Force refresh everything")
        print("  python run.py listings 20              # Scrape 20 car versions")
        sys.exit(1)

    args = parser.parse_args()
    orchestrator = CarScrapingOrchestrator()

    # Handle skip-brands-models or only-versions flags
    skip_brands_models = args.skip_brands_models or args.only_versions
    force_refresh = args.force_refresh

    if args.command == "index":
        orchestrator.scrape_index_data(skip_brands_models=skip_brands_models,
                                     force_refresh=force_refresh)
    elif args.command == "listings":
        orchestrator.scrape_car_listings(limit_versions=args.limit)
    elif args.command == "full":
        orchestrator.run_full_scrape(skip_brands_models=skip_brands_models,
                                   force_refresh=force_refresh)

if __name__ == "__main__":
    main()