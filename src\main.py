#!/usr/bin/env python3
"""
Main orchestrator for car scraping system
"""

import sys
import os
import logging
from datetime import datetime
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.scrapers.bonbanh_scraper import Bonbanh<PERSON>craper
from src.models.car_service import car_service
from src.models.database import db_manager

class CarScrapingOrchestrator:
    def __init__(self):
        self.scraper = BonbanhScraper()
        self.logger = logging.getLogger(__name__)
        
        # Create logs directory if it doesn't exist
        os.makedirs('logs', exist_ok=True)
        
        # Set up file logging with UTF-8 encoding
        file_handler = logging.FileHandler('logs/scraper.log', encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)

        # Set up console logging with UTF-8 encoding
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)

        # Set logger level
        self.logger.setLevel(logging.INFO)

    def scrape_index_data(self):
        """Scrape and store index data (brands, models, car versions)"""
        self.logger.info("[START] Starting index data scraping...")
        
        # Step 1: Scrape brands and models
        brands_data = self.scraper.scrape_brands_and_models()
        
        if not brands_data:
            self.logger.error("[ERROR] No brands data found. Check the scraper logic.")
            return False
        
        # Step 2: For each brand/model combination, scrape available years and versions
        years_to_scrape = [2020, 2021, 2022, 2023, 2024, 2025]  # Focus on recent years
        
        for brand in brands_data:
            brand_id = brand['id']
            brand_slug = brand['slug']
            
            self.logger.info(f"[BRAND] Processing brand: {brand['name']}")
            
            for model in brand['models']:
                model_id = model['id']
                model_slug = model['slug']
                
                self.logger.info(f"  [MODEL] Processing model: {model['name']}")
                
                for year in years_to_scrape:
                    # Insert year if not exists
                    car_service.insert_year(year)
                    
                    # Scrape versions for this brand/model/year
                    versions = self.scraper.scrape_car_versions(brand_slug, model_slug, year)
                    
                    for version in versions:
                        version_id = car_service.insert_car_version_year(
                            brand_id=brand_id,
                            model_id=model_id,
                            year=year,
                            version_name=version['version_name'],
                            url=version['url']
                        )
                        
                        if version_id:
                            self.logger.info(f"    [SUCCESS] Added version: {version['version_name']} ({year})")
        
        self.logger.info("[COMPLETE] Index data scraping completed!")
        return True

    def scrape_car_listings(self, limit_versions: int = 50):
        """Scrape individual car listings for stored versions"""
        self.logger.info("[START] Starting car listings scraping...")
        
        # Get versions that need to be scraped
        versions_to_scrape = car_service.get_car_versions_to_scrape(limit=limit_versions)
        
        if not versions_to_scrape:
            self.logger.info("[INFO] No versions need to be scraped at this time.")
            return True
        
        self.logger.info(f"[INFO] Found {len(versions_to_scrape)} versions to scrape")
        
        for i, version in enumerate(versions_to_scrape, 1):
            self.logger.info(f"[{i}/{len(versions_to_scrape)}] Scraping: {version['brand_name']} "
                           f"{version['model_name']} {version['version_name']} ({version['year']})")
            
            # Scrape listings for this version (first 3 pages)
            listings = self.scraper.scrape_car_listings(version['url'], max_pages=3)
            
            if not listings:
                self.logger.warning(f"[WARNING] No listings found for {version['url']}")
                continue
            
            # Insert listings into database
            inserted_count = 0
            for listing in listings:
                item_id = car_service.insert_car_item(
                    car_version_year_id=version['id'],
                    car_name=listing['car_name'],
                    url=listing['url'],
                    price=listing['price'],
                    location=listing.get('location'),
                    year=listing.get('year')
                )
                
                if item_id:
                    inserted_count += 1
            
            self.logger.info(f"  [SUCCESS] Inserted {inserted_count} listings")
            
            # Update price statistics
            car_service.update_car_version_prices(version['id'])
            
        self.logger.info("[COMPLETE] Car listings scraping completed!")
        return True

    def run_full_scrape(self):
        """Run complete scraping process"""
        start_time = datetime.now()
        self.logger.info(f"[START] Starting full scraping process at {start_time}")
        
        try:
            # Step 1: Scrape index data
            if not self.scrape_index_data():
                self.logger.error("[ERROR] Index data scraping failed")
                return False
            
            # Step 2: Scrape car listings
            if not self.scrape_car_listings():
                self.logger.error("[ERROR] Car listings scraping failed")
                return False
            
            end_time = datetime.now()
            duration = end_time - start_time
            self.logger.info(f"[COMPLETE] Full scraping process completed in {duration}")
            
            return True
            
        except KeyboardInterrupt:
            self.logger.info("[STOP] Scraping interrupted by user")
            return False
        except Exception as e:
            self.logger.error(f"[ERROR] Scraping failed with error: {e}", exc_info=True)
            return False
        finally:
            db_manager.disconnect()

def main():
    """Main entry point"""
    orchestrator = CarScrapingOrchestrator()
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "index":
            orchestrator.scrape_index_data()
        elif command == "listings":
            limit = int(sys.argv[2]) if len(sys.argv) > 2 else 50
            orchestrator.scrape_car_listings(limit_versions=limit)
        elif command == "full":
            orchestrator.run_full_scrape()
        else:
            print("Usage: python src/main.py [index|listings|full] [limit]")
            sys.exit(1)
    else:
        # Default: run full scrape
        orchestrator.run_full_scrape()

if __name__ == "__main__":
    main()