{
    "command": "uvx",
    "args": [
        "--from",
        "git+https://github.com/oraios/serena",
        "serena-mcp-server",
        "--context",
        "ide-assistant"
    ]
}

{
    "mcpServers": {
        "serena": {
            "command": "C:\\Users\\<USER>\\.local\\bin\\uv.exe",
            "args": [
                "run",
                "--directory",
                "D:\\Projects\\CaNhan\\serena",
                "serena",
                "start-mcp-server"
            ]
        }
    }
}

claude mcp add-json serena '{
    "command": "C:\\Users\\<USER>\\.local\\bin\\uv.exe",
    "args": [
        "run",
        "--directory",
        "D:\\Projects\\CaNhan\\serena",
        "serena",
        "start-mcp-server"
    ]
}'

claude mcp add serena -- "C:\Users\<USER>\.local\bin\uv.exe"