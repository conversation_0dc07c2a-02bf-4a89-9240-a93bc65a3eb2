#!/usr/bin/env python3
"""
Price calculation and statistics utilities
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.models.database import db_manager
import logging

class PriceCalculator:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def recalculate_all_prices(self):
        """Recalculate price statistics for all car version years"""
        self.logger.info("🧮 Starting price recalculation for all car versions...")
        
        try:
            # Get all car version year IDs
            version_ids = db_manager.execute_query("""
                SELECT id FROM car_version_year
            """, fetch=True)
            
            if not version_ids:
                self.logger.info("ℹ️ No car versions found to process")
                return True
            
            total_versions = len(version_ids)
            self.logger.info(f"📋 Processing {total_versions} car versions...")
            
            updated_count = 0
            for i, (version_id,) in enumerate(version_ids, 1):
                if self.recalculate_version_prices(version_id):
                    updated_count += 1
                
                if i % 10 == 0:  # Progress update every 10 versions
                    self.logger.info(f"Progress: {i}/{total_versions} versions processed")
            
            self.logger.info(f"✅ Price recalculation completed! Updated {updated_count} versions")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Price recalculation failed: {e}")
            return False
    
    def recalculate_version_prices(self, car_version_year_id: int) -> bool:
        """Recalculate price statistics for a specific car version year"""
        try:
            # Get price statistics from car_items
            stats = db_manager.execute_query("""
                SELECT 
                    COUNT(*) as total_listings,
                    AVG(price) as avg_price,
                    MIN(price) as min_price,
                    MAX(price) as max_price
                FROM car_items 
                WHERE car_version_year_id = %s AND price > 0
            """, (car_version_year_id,), fetch=True)
            
            if not stats or stats[0][0] == 0:
                # No listings found, set prices to NULL
                db_manager.execute_query("""
                    UPDATE car_version_year 
                    SET total_listings = 0, average_price = NULL, 
                        min_price = NULL, max_price = NULL, updated_at = NOW()
                    WHERE id = %s
                """, (car_version_year_id,))
                return True
            
            total_listings, avg_price, min_price, max_price = stats[0]
            
            # Update car_version_year with calculated statistics
            db_manager.execute_query("""
                UPDATE car_version_year 
                SET total_listings = %s, average_price = %s, min_price = %s, 
                    max_price = %s, updated_at = NOW()
                WHERE id = %s
            """, (total_listings, avg_price, min_price, max_price, car_version_year_id))
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to recalculate prices for version {car_version_year_id}: {e}")
            return False
    
    def generate_price_report(self):
        """Generate a price report for all car versions"""
        self.logger.info("📊 Generating price report...")
        
        try:
            # Get comprehensive statistics
            report_data = db_manager.execute_query("""
                SELECT 
                    b.name as brand_name,
                    m.name as model_name,
                    cv.year,
                    cv.version_name,
                    cv.total_listings,
                    cv.min_price,
                    cv.average_price,
                    cv.max_price,
                    cv.scraped_at
                FROM car_version_year cv
                JOIN brands b ON cv.brand_id = b.id
                JOIN models m ON cv.model_id = m.id
                WHERE cv.total_listings > 0
                ORDER BY b.name, m.name, cv.year DESC, cv.version_name
            """, fetch=True)
            
            if not report_data:
                self.logger.info("ℹ️ No data available for price report")
                return
            
            print("\n" + "="*100)
            print("🚗 CAR PRICE REPORT")
            print("="*100)
            print(f"{'Brand':<15} {'Model':<15} {'Year':<6} {'Version':<25} {'Listings':<8} {'Min Price':<12} {'Avg Price':<12} {'Max Price':<12}")
            print("-"*100)
            
            for row in report_data:
                brand, model, year, version, listings, min_price, avg_price, max_price, scraped_at = row
                
                min_price_str = f"{min_price:,.0f}" if min_price else "N/A"
                avg_price_str = f"{avg_price:,.0f}" if avg_price else "N/A" 
                max_price_str = f"{max_price:,.0f}" if max_price else "N/A"
                
                print(f"{brand:<15} {model:<15} {year:<6} {version:<25} {listings:<8} {min_price_str:<12} {avg_price_str:<12} {max_price_str:<12}")
            
            print("="*100)
            print(f"Total car versions with data: {len(report_data)}")
            
            # Summary statistics
            total_listings = sum(row[4] for row in report_data if row[4])
            print(f"Total individual car listings: {total_listings:,}")
            print("="*100)
            
        except Exception as e:
            self.logger.error(f"Failed to generate price report: {e}")
    
    def clean_invalid_prices(self):
        """Clean up invalid price entries"""
        self.logger.info("🧹 Cleaning invalid price entries...")
        
        try:
            # Remove car items with invalid prices
            deleted_count = db_manager.execute_query("""
                DELETE FROM car_items WHERE price IS NULL OR price <= 0
            """)
            
            if deleted_count:
                self.logger.info(f"🗑️ Removed {deleted_count} invalid price entries")
            
            # Recalculate prices after cleanup
            self.recalculate_all_prices()
            
        except Exception as e:
            self.logger.error(f"Failed to clean invalid prices: {e}")

def main():
    """Main entry point for price calculator"""
    calculator = PriceCalculator()
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "recalculate":
            calculator.recalculate_all_prices()
        elif command == "report":
            calculator.generate_price_report()
        elif command == "clean":
            calculator.clean_invalid_prices()
        else:
            print("Usage: python src/utils/price_calculator.py [recalculate|report|clean]")
            sys.exit(1)
    else:
        # Default: generate report
        calculator.generate_price_report()

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    main()