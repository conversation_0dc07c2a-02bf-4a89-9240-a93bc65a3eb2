# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Vietnamese car listing web scraper project that extracts car data from bonbanh.com and stores it in a MySQL database. The system scrapes:

- Car brands and models
- Car years/generations  
- Car versions/trims with pricing data
- Individual car listings

## Project Requirements

Based on `docs/prompt.txt`, the system should:

1. Scrape car data from bonbanh.com (example: https://bonbanh.com/oto/bmw-x3-nam-2025)
2. Extract hierarchical car data: Brand → Model → Year → Version
3. Store data in MySQL with these key tables:
   - Car brands and models
   - `car_version_year` table with columns: brand_id, model_id, year, version_name, url, average_price, max_price, min_price
   - `car_items` table with individual car listings: car_version_year_id, name, url, price
4. Calculate pricing statistics (min/max/average) for each version

## Development Setup

This appears to be a new project with no existing code structure. When implementing:

1. Use Python for web scraping 
2. Connect to MySQL database using credentials from `.env` file
3. Organize code into logical directory structure (not all files in root)
4. <PERSON>rape first 3 pages of listings for each car version

## Database Connection

Database connection details should be stored in `.env` file (not yet created).

## Project Structure Recommendations

Since this is a greenfield project, organize code into:
- `src/` for main application code
- `models/` for database models
- `scrapers/` for web scraping logic  
- `config/` for configuration management
- `requirements.txt` for Python dependencies