# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Vietnamese car listing web scraper project that extracts car data from bonbanh.com and stores it in a MySQL database. The system scrapes:

- Car brands and models
- Car years/generations  
- Car versions/trims with pricing data
- Individual car listings

## Project Requirements

Based on `docs/prompt.txt`, the system should:

1. Scrape car data from bonbanh.com (example: https://bonbanh.com/oto/bmw-x3-nam-2025)
2. Extract hierarchical car data: Brand → Model → Year → Version
3. Store data in MySQL with these key tables:
   - Car brands and models
   - `car_version_year` table with columns: brand_id, model_id, year, version_name, url, average_price, max_price, min_price
   - `car_items` table with individual car listings: car_version_year_id, name, url, price
4. Calculate pricing statistics (min/max/average) for each version

## Development Setup

### Project Structure
```
src/
├── config/          # Database and configuration management
│   ├── __init__.py
│   └── database.py
├── models/          # Database models and services
│   ├── __init__.py
│   ├── database.py
│   └── car_service.py
├── scrapers/        # Web scraping logic
│   ├── __init__.py
│   └── bonbanh_scraper.py
├── utils/           # Utility functions
│   ├── __init__.py
│   ├── helpers.py
│   └── price_calculator.py
├── __init__.py
├── init_database.py # Database initialization
└── main.py         # Main orchestrator

# Root level convenience scripts
run.py              # Main scraper entry point
setup_db.py         # Database setup
price_report.py     # Price statistics and reports
requirements.txt    # Python dependencies
```

### Installation and Setup

1. Install Python dependencies:
```bash
pip install -r requirements.txt
```

2. Set up database (ensure MySQL is running and database exists):
```bash
python setup_db.py
```

3. Run the scraper:
```bash
# Full scraping (index data + car listings)
python run.py full

# Only scrape index data (brands, models, versions)
python run.py index

# Only scrape car listings (max 50 versions)
python run.py listings

# Scrape specific number of car versions
python run.py listings 20
```

4. Generate price reports:
```bash
# Generate price statistics report
python price_report.py report

# Recalculate all price statistics
python price_report.py recalculate

# Clean invalid price entries
python price_report.py clean
```

## Database Connection

Database connection details should be stored in `.env` file (not yet created).

## Project Structure Recommendations

Since this is a greenfield project, organize code into:
- `src/` for main application code
- `models/` for database models
- `scrapers/` for web scraping logic  
- `config/` for configuration management
- `requirements.txt` for Python dependencies