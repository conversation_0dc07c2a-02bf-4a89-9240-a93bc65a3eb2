## Project Purpose
This project is a Python-based web scraper for bonbanh.com, designed to collect car data and index it into a MySQL database. It extracts:
- Car brands
- Car models
- Car years
- Car versions by year (table: car_version_year)
- Car items for each version (table: car_items)
It also calculates average, min, and max prices for each car version per year.

## Tech Stack
- Python
- MySQL
- .env for configuration
- Likely libraries: requests, BeautifulSoup, mysql-connector-python or SQLAlchemy

## Code Structure
- Python code should be organized into logical folders (e.g., `src/`, `db/`, `scraper/`, `models/`).
- No code in the project root.
- Configuration in `.env`.

## Entry Points
- Main script to run the scraper (to be created, e.g., `src/main.py`).

## Windows System
- Use PowerShell commands for system operations.

## Guidelines
- Follow Python best practices: PEP8, type hints, docstrings.
- Use environment variables for DB connection.
- Log scraping activity.
- Handle retries, delays, and timeouts as per `.env`.

## Next Steps
- Scaffold Python folders and files.
- Implement database models and connection.
- Implement web scraping logic.
- Implement data insertion and price calculation.
- Add logging and error handling.