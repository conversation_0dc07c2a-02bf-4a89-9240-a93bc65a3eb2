Serena hãy active dự án này, sau đó tạo initial instructions, tiếp theo thực hiện onboarding dự án.

Tiếp theo hãy thực hiện việc sau:

Tự truy cập vào URL mẫu: https://bonbanh.com/oto/bmw-x3-nam-2025 để hiểu cấu trúc html của website
Sẽ thấy các phần chính:

- <PERSON>u hiển thị hãng và model xe
- box hiển thị các năm (đời xe) đang bán
- box hiển thị các phiên bản xe của đời đó.

Trong link ví dụ thì có các thông tin:

Hãng xe là BMW
Model xe là X3
Đời xe là 2025
Phiên bản xe có các mẫu: 
- sDrive20i M Sport
- xDrive20i
- xDrive20i M Sport

Hãy sử dụng python để truy cập website và lập các dữ liệu chỉ mục sau vào database mysql có thông tin kết nối trong file .env
- <PERSON>h sách hãng xe
- <PERSON>h sách các đời xe
- Danh sách các phiên bản xe theo năm (tablename: car_version_year) (có các cột brand_id, model_id, year, version_name, url (ví dụ https://bonbanh.com/oto/bmw-x3-sdrive20i-m-sport-nam-2025), giá trung bình, giá cao nhất giá thấp nhất)

Ví dụ Trong link https://bonbanh.com/oto/bmw-x3-sdrive20i-m-sport-nam-2025 sẽ có danh sách các xe thuộc phiên bản "sDrive20i M Sport" năm 2025
Với mỗi 1 link phiên bản xe như thế này lấy danh sách 3 trang xe đầu tiên, và lưu vào database bảng car_items các thông tin sau:
- car_version_year_id
- Tên xe: ví dụ: Xe BMW X3 sDrive20i M Sport 2025
- Url: ví dụ https://bonbanh.com/xe-bmw-x3-sdrive20i-m-sport-2025-5765980
- Giá 2.189.000.000 (2 Tỷ 189 Triệu)

Dựa vào đó tính toán lại giá trung bình , giá min, max của bảng car_version_year

Chú ý: code file python được tổ chức vào các thư mục hợp lý, không để toàn bộ file vào thư mục gốc