## Suggested Commands

### Run Scraper
- `python src/main.py` (after implementation)

### Install dependencies
- `pip install -r requirements.txt`

### Linting
- `python -m flake8 src/`

### Formatting
- `python -m black src/`

### Testing
- `python -m unittest discover tests`

### System Utilities (Windows)
- `ls` or `dir` (list files)
- `cd <folder>` (change directory)
- `findstr <pattern> <file>` (search in files)
- `git` (version control)

### Database
- Use MySQL Workbench or `mysql` CLI for DB management

### Logging
- Check logs in `logs/scraper.log`